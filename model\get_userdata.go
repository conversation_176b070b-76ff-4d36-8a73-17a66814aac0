package model

import (
	"database/sql"
	"log"

	_ "github.com/mattn/go-sqlite3"
)

const userdb string = "userdata.sqlite3"

type user struct {
	userid   int
	username string
}

func GetUsername(uid int) (string, error) {
	DbConnection, err := sql.Open("sqlite3", userdb)
	if err != nil {
		log.Fatalf("failed to open \"userdata.sqlite3\":%v", err)
	}
	defer DbConnection.Close()

	u, err := Username_from_id(DbConnection, uid)

	if err != nil {
		return "noname", err
	}

	return u.username, nil

}

func Username_from_id(DbConnection *sql.DB, id int) (user, error) {
	var u user
	query_select_from_id := "SELECT userid,username FROM user WHERE userid=?"
	row := DbConnection.QueryRow(query_select_from_id, id)

	err := row.Scan(&u.userid, &u.username)
	if err != nil {
		return u, err
	}

	return u, nil
}
