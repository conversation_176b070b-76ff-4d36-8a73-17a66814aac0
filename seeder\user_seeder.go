package seeder

import (
	"database/sql"
	"fmt"
	"golangTest/model"
	"log"
	"os"

	"github.com/joho/godotenv"

	_ "github.com/mattn/go-sqlite3"
)

type db struct {
	dbtype       string
	dbname       string
	tablename    string
	DbConnection *sql.DB
}

type user struct {
	userid     int
	username   string
	createdate string
	password   string
}

func (d *db) DBSettingFromenv(tablename string) {
	err := godotenv.Load(".env")
	if err != nil {
		fmt.Println(err)
	}
	d.dbtype = os.Getenv("DB")
	d.dbname = os.Getenv("DBNAME")
	d.tablename = tablename
}

func (d *db) OpenDB() error {
	DbConnection, err := sql.Open(d.dbtype, d.dbname)
	if err != nil {
		return err
	}
	d.DbConnection = DbConnection
	return nil
}

func insertData(d db, u []user) {
	for i := range u {
		pass := model.Genhash(i.password)
		ins := "INSERT INTO user()"
	}
}

func main() {
	d := &db{}
	d.DBSettingFromenv("user")
	d.OpenDB()

	testdata := []user{
		{userid: 1, username: "A", password: "password1"},
		{userid: 2, username: "B", password: "password2"},
		{userid: 3, username: "C", password: "password3"},
		{userid: 4, username: "D", password: "password4"},
		{userid: 5, username: "E", password: "password5"},
	}

	log.Println("Userdataの追加を試みます。")
}
