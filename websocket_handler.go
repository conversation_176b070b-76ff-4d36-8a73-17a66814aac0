package main

import (
	"fmt"
	"golangTest/model"
	"log"
	"net/http"
	"strconv"

	"github.com/gorilla/websocket"
)

var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool { return true },
}

type Client struct {
	conn     *websocket.Conn
	UID      int
	Username string
}

type Hub struct {
	clients    map[*Client]bool
	broadcast  chan []byte
	register   chan *Client
	unregister chan *Client
}

func NewHub() *Hub {
	return &Hub{
		clients:    make(map[*Client]bool),
		broadcast:  make(chan []byte),
		register:   make(chan *Client),
		unregister: make(chan *Client),
	}
}

func (h *Hub) Run() {
	for {
		select {
		case client := <-h.register:
			h.clients[client] = true
			log.Println("Client registered. Total clients:%d", len(h.clients))

		case client := <-h.unregister:
			if h.clients[client] {
				delete(h.clients, client)
				client.conn.Close()
				log.Printf("Client unregistered. Total clients:%d", len(h.clients))
			}
		case message := <-h.broadcast:
			log.Printf("broadcast message:%s", message)
			for client := range h.clients {
				err := client.conn.WriteMessage(websocket.TextMessage, message)
				if err != nil {
					log.Printf("Error writing to client: %v,attempting to unregister", err)
					delete(h.clients, client)
					client.conn.Close()
					log.Printf("Client unregistered. Total clients:%d", len(h.clients))
				}
			}
		}
	}
}

var globalHub *Hub

func handleWebSocket(w http.ResponseWriter, r *http.Request) {
	conn, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Println("WebSocket connection failed:", err)
		return
	}
	defer conn.Close()

	uidstr := r.URL.Query().Get("uid")
	if uidstr == "" {
		log.Println("cannot identificate uid")
		return
	}

	uid, err := strconv.Atoi(uidstr)
	if err != nil {
		log.Printf("uid is not num:", err)
		return
	}

	username, unerr := model.GetUsername(uid)
	if unerr != nil {
		log.Printf("no data in databass :%v", err)
		return
	}

	client := &Client{
		conn:     conn,
		UID:      uid,
		Username: username,
	}

	globalHub.register <- client
	defer func() { globalHub.unregister <- client }()

	for {
		_, msg, err := conn.ReadMessage()
		if err != nil {
			log.Println("Read error:", err)
			break
		}
		fmt.Println("Received from client: %s\n", msg)

		globalHub.broadcast <- msg
	}

}

func main() {
	globalHub = NewHub()
	go globalHub.Run()

	http.HandleFunc("/ws", handleWebSocket)
	fmt.Println("WebSocket server started on ws://localhost:8080/ws")
	log.Fatal(http.ListenAndServe(":8080", nil))

}
